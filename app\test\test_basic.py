import pytest
import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from fastapi.testclient import TestClient
from main import app

client = TestClient(app)

def test_health_check():
    """Test the health check endpoint"""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"
    assert "timestamp" in data

def test_root_endpoint():
    """Test the root endpoint returns HTML"""
    response = client.get("/")
    assert response.status_code == 200
    assert "text/html" in response.headers["content-type"]

def test_analyze_video_no_input():
    """Test analyze video endpoint with no input"""
    response = client.post("/analyze-video")
    assert response.status_code == 400
    data = response.json()
    assert "Either video file or video URL must be provided" in data["detail"]

def test_analyze_video_both_inputs():
    """Test analyze video endpoint with both file and URL"""
    # Create a dummy file
    files = {"video_file": ("test.mp4", b"fake video content", "video/mp4")}
    data = {"video_url": "http://example.com/video.mp4"}
    
    response = client.post("/analyze-video", files=files, data=data)
    assert response.status_code == 400
    response_data = response.json()
    assert "Provide either video file or video URL, not both" in response_data["detail"]

if __name__ == "__main__":
    pytest.main([__file__])
