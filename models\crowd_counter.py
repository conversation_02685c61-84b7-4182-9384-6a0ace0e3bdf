import cv2
import numpy as np
from typing import List, Dict, Tuple
import logging
from scipy import ndimage
from sklearn.cluster import DBSCAN

logger = logging.getLogger(__name__)

class SimpleCrowdCounter:
    """
    Simple crowd counting based on YOLO detections and density estimation
    This is a POC implementation - in production, you'd use CSRNet or similar
    """
    
    def __init__(self):
        self.density_kernel_size = 15
        self.clustering_eps = 50  # DBSCAN epsilon parameter
        self.clustering_min_samples = 2
        
    def estimate_crowd_density(self, detections: List[Dict], image_shape: Tuple[int, int]) -> np.ndarray:
        """
        Create a density map based on person detections
        
        Args:
            detections: List of person detections from YOLO
            image_shape: (height, width) of the image
            
        Returns:
            Density heatmap as numpy array
        """
        height, width = image_shape[:2]
        density_map = np.zeros((height, width), dtype=np.float32)
        
        if not detections:
            return density_map
        
        # Create Gaussian kernels at detection centers
        for detection in detections:
            center_x, center_y = detection['center']
            confidence = detection['confidence']
            
            # Create a Gaussian kernel around each detection
            y, x = np.ogrid[:height, :width]
            mask = ((x - center_x) ** 2 + (y - center_y) ** 2) <= (self.density_kernel_size ** 2)
            
            # Apply Gaussian with confidence weighting
            gaussian = np.exp(-((x - center_x) ** 2 + (y - center_y) ** 2) / (2 * (self.density_kernel_size / 3) ** 2))
            density_map += mask * gaussian * confidence
        
        # Normalize density map
        if density_map.max() > 0:
            density_map = density_map / density_map.max()
        
        return density_map
    
    def analyze_crowd_distribution(self, detections: List[Dict]) -> Dict:
        """
        Analyze the spatial distribution of detected persons
        
        Args:
            detections: List of person detections
            
        Returns:
            Dictionary with crowd analysis metrics
        """
        if not detections:
            return {
                'total_count': 0,
                'clusters': [],
                'density_score': 0.0,
                'distribution': 'empty'
            }
        
        # Extract center points
        centers = np.array([det['center'] for det in detections])
        
        # Perform clustering to identify crowd groups
        if len(centers) >= self.clustering_min_samples:
            clustering = DBSCAN(eps=self.clustering_eps, min_samples=self.clustering_min_samples)
            cluster_labels = clustering.fit_predict(centers)
            
            # Analyze clusters
            unique_labels = set(cluster_labels)
            clusters = []
            
            for label in unique_labels:
                if label == -1:  # Noise points
                    continue
                
                cluster_points = centers[cluster_labels == label]
                cluster_center = np.mean(cluster_points, axis=0)
                cluster_size = len(cluster_points)
                
                clusters.append({
                    'id': int(label),
                    'center': cluster_center.tolist(),
                    'size': cluster_size,
                    'points': cluster_points.tolist()
                })
        else:
            clusters = []
        
        # Calculate density score
        if len(detections) > 1:
            # Calculate average distance between detections
            distances = []
            for i, det1 in enumerate(detections):
                for j, det2 in enumerate(detections[i+1:], i+1):
                    dist = np.sqrt((det1['center'][0] - det2['center'][0])**2 + 
                                 (det1['center'][1] - det2['center'][1])**2)
                    distances.append(dist)
            
            avg_distance = np.mean(distances) if distances else 0
            density_score = 1.0 / (1.0 + avg_distance / 100.0)  # Normalize to 0-1
        else:
            density_score = 0.1 if len(detections) == 1 else 0.0
        
        # Determine distribution type
        if len(detections) == 0:
            distribution = 'empty'
        elif len(detections) <= 5:
            distribution = 'sparse'
        elif len(detections) <= 20:
            distribution = 'moderate'
        elif len(detections) <= 50:
            distribution = 'dense'
        else:
            distribution = 'very_dense'
        
        return {
            'total_count': len(detections),
            'clusters': clusters,
            'density_score': density_score,
            'distribution': distribution,
            'avg_confidence': np.mean([det['confidence'] for det in detections]) if detections else 0.0
        }
    
    def create_density_visualization(self, density_map: np.ndarray) -> np.ndarray:
        """
        Create a colored visualization of the density map
        
        Args:
            density_map: Density heatmap
            
        Returns:
            Colored density visualization
        """
        # Normalize to 0-255
        normalized = (density_map * 255).astype(np.uint8)
        
        # Apply colormap (hot colormap: black -> red -> yellow -> white)
        colored = cv2.applyColorMap(normalized, cv2.COLORMAP_HOT)
        
        return colored
    
    def overlay_density_on_image(self, image: np.ndarray, density_map: np.ndarray, alpha: float = 0.4) -> np.ndarray:
        """
        Overlay density visualization on original image
        
        Args:
            image: Original image
            density_map: Density heatmap
            alpha: Transparency of overlay
            
        Returns:
            Image with density overlay
        """
        # Create colored density visualization
        density_colored = self.create_density_visualization(density_map)
        
        # Resize density map to match image if needed
        if density_colored.shape[:2] != image.shape[:2]:
            density_colored = cv2.resize(density_colored, (image.shape[1], image.shape[0]))
        
        # Blend with original image
        overlay = cv2.addWeighted(image, 1 - alpha, density_colored, alpha, 0)
        
        return overlay
    
    def process_frame_with_density(self, image: np.ndarray, detections: List[Dict]) -> Dict:
        """
        Process frame with crowd density analysis
        
        Args:
            image: Input image
            detections: Person detections from YOLO
            
        Returns:
            Dictionary with analysis results and visualizations
        """
        # Create density map
        density_map = self.estimate_crowd_density(detections, image.shape)
        
        # Analyze crowd distribution
        crowd_analysis = self.analyze_crowd_distribution(detections)
        
        # Create visualizations
        density_visualization = self.create_density_visualization(density_map)
        density_overlay = self.overlay_density_on_image(image, density_map)
        
        return {
            'crowd_analysis': crowd_analysis,
            'density_map': density_map,
            'density_visualization': density_visualization,
            'density_overlay': density_overlay,
            'estimated_count': crowd_analysis['total_count'],
            'density_score': crowd_analysis['density_score']
        }
    
    def get_model_info(self) -> Dict:
        """Get information about the crowd counting model"""
        return {
            "model_type": "Simple Density-based Counter",
            "version": "POC v1.0",
            "description": "YOLO detection + density estimation",
            "clustering_algorithm": "DBSCAN",
            "density_kernel_size": self.density_kernel_size
        }
