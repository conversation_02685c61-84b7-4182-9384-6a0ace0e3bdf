#!/usr/bin/env python3
"""
Test script for video upload and analysis
"""

import requests
import json
import os

def test_video_upload(video_path="realistic_test_video.mp4"):
    """Test video analysis with file upload"""
    
    if not os.path.exists(video_path):
        print(f"❌ Video file not found: {video_path}")
        print("Please run 'python create_test_video.py' first to create a test video.")
        return False
    
    print(f"Testing video analysis with file upload...")
    print(f"Video file: {video_path}")
    print(f"File size: {os.path.getsize(video_path) / 1024:.2f} KB")
    
    try:
        # Prepare the file for upload
        with open(video_path, 'rb') as video_file:
            files = {
                'video_file': (video_path, video_file, 'video/mp4')
            }
            
            print("Uploading and analyzing video...")
            print("This may take a few moments...")
            
            # Send request
            response = requests.post(
                "http://localhost:8000/analyze-video", 
                files=files,
                timeout=120  # 2 minute timeout
            )
        
        if response.status_code == 200:
            results = response.json()
            print("\n✅ Analysis successful!")
            print("=" * 50)
            print(f"📊 RESULTS SUMMARY")
            print("=" * 50)
            print(f"Total estimated count: {results['total_estimated_count']}")
            print(f"Average count per frame: {results['average_count_per_frame']:.2f}")
            print(f"Total frames analyzed: {results['total_frames_analyzed']}")
            print(f"Annotated frames available: {len(results['annotated_frames'])}")
            
            # Print annotated frame URLs
            if results['annotated_frames']:
                print(f"\n🖼️  Sample annotated frames:")
                for i, frame_url in enumerate(results['annotated_frames'], 1):
                    print(f"  {i}. http://localhost:8000{frame_url}")
            
            # Print model information
            if 'details' in results and 'model_versions' in results['details']:
                print(f"\n🤖 Model Information:")
                yolo_info = results['details']['model_versions']['yolo']
                crowd_info = results['details']['model_versions']['crowd_counting']
                
                if isinstance(yolo_info, dict):
                    print(f"  YOLO: {yolo_info.get('model_type', 'Unknown')} - {yolo_info.get('status', 'Unknown')}")
                else:
                    print(f"  YOLO: {yolo_info}")
                
                if isinstance(crowd_info, dict):
                    print(f"  Crowd Counter: {crowd_info.get('model_type', 'Unknown')}")
                else:
                    print(f"  Crowd Counter: {crowd_info}")
            
            # Print summary statistics if available
            if 'details' in results and 'summary_statistics' in results['details']:
                stats = results['details']['summary_statistics']
                print(f"\n📈 Summary Statistics:")
                print(f"  Max count per frame: {stats.get('max_count_per_frame', 'N/A')}")
                print(f"  Min count per frame: {stats.get('min_count_per_frame', 'N/A')}")
                print(f"  Average density score: {stats.get('avg_density_score', 'N/A'):.3f}")
            
            # Print frame-by-frame results (first few frames)
            if 'details' in results and 'frames' in results['details']:
                frames = results['details']['frames'][:3]  # Show first 3 frames
                print(f"\n🎬 Frame-by-frame Analysis (first 3 frames):")
                for frame in frames:
                    print(f"  Frame {frame['frame_number']}: {frame['detections_count']} persons detected")
                    print(f"    Timestamp: {frame['timestamp']:.2f}s")
                    print(f"    Density score: {frame['density_score']:.3f}")
                    if frame['crowd_analysis']['distribution'] != 'empty':
                        print(f"    Distribution: {frame['crowd_analysis']['distribution']}")
            
            print(f"\n🌐 Web Interface:")
            print(f"  Open http://localhost:8000 to use the web interface")
            print(f"  View annotated frames at the URLs listed above")
            
            return True
            
        else:
            print(f"❌ Request failed with status code: {response.status_code}")
            try:
                error_detail = response.json()
                print(f"Error details: {error_detail}")
            except:
                print(f"Error response: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ Request timed out. The video might be too large or processing is taking too long.")
        return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def main():
    print("🎥 AI Crowd POC - Testing Video Upload and Analysis")
    print("=" * 60)
    
    # Check if server is running
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code != 200:
            print("❌ Server is not responding properly. Please check if it's running.")
            return
    except:
        print("❌ Cannot connect to server. Please make sure it's running on http://localhost:8000")
        print("Run: python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload")
        return
    
    print("✅ Server is running")
    
    # Test video upload
    success = test_video_upload()
    
    if success:
        print("\n🎉 Test completed successfully!")
    else:
        print("\n❌ Test failed. Check the server logs for more details.")

if __name__ == "__main__":
    main()
