<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Crowd POC - Person Counting</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .upload-section {
            border: 2px dashed #ddd;
            padding: 30px;
            text-align: center;
            margin-bottom: 20px;
            border-radius: 8px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input[type="file"], input[type="url"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .results {
            margin-top: 30px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
            display: none;
        }
        .loading {
            text-align: center;
            padding: 20px;
            display: none;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
            margin: 0 auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .result-item {
            margin-bottom: 15px;
            padding: 10px;
            background-color: white;
            border-radius: 4px;
        }
        .frames-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .frame-item img {
            width: 100%;
            height: auto;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎥 AI Crowd POC - Person Counting</h1>
        <p style="text-align: center; color: #666; margin-bottom: 30px;">
            Upload a video or provide a URL to analyze crowd density and count people
        </p>
        
        <form id="uploadForm" enctype="multipart/form-data">
            <div class="upload-section">
                <div class="form-group">
                    <label for="videoFile">Upload Video File:</label>
                    <input type="file" id="videoFile" name="video_file" accept="video/*">
                </div>
                
                <div style="margin: 20px 0; color: #666;">OR</div>
                
                <div class="form-group">
                    <label for="videoUrl">Video URL:</label>
                    <input type="url" id="videoUrl" name="video_url" placeholder="https://example.com/video.mp4">
                </div>
            </div>
            
            <button type="submit" id="analyzeBtn">🔍 Analyze Video</button>
        </form>
        
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>Processing video... This may take a few minutes.</p>
        </div>
        
        <div class="results" id="results">
            <h2>📊 Analysis Results</h2>
            <div id="resultsContent"></div>
        </div>
    </div>

    <script>
        document.getElementById('uploadForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const videoFile = document.getElementById('videoFile').files[0];
            const videoUrl = document.getElementById('videoUrl').value;
            
            if (!videoFile && !videoUrl) {
                alert('Please provide either a video file or a video URL');
                return;
            }
            
            if (videoFile && videoUrl) {
                alert('Please provide either a video file or a video URL, not both');
                return;
            }
            
            // Show loading
            document.getElementById('loading').style.display = 'block';
            document.getElementById('results').style.display = 'none';
            document.getElementById('analyzeBtn').disabled = true;
            
            try {
                const formData = new FormData();
                if (videoFile) {
                    formData.append('video_file', videoFile);
                } else {
                    formData.append('video_url', videoUrl);
                }
                
                const response = await fetch('/analyze-video', {
                    method: 'POST',
                    body: formData
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const results = await response.json();
                displayResults(results);
                
            } catch (error) {
                console.error('Error:', error);
                document.getElementById('resultsContent').innerHTML = 
                    `<div class="error">Error: ${error.message}</div>`;
                document.getElementById('results').style.display = 'block';
            } finally {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('analyzeBtn').disabled = false;
            }
        });
        
        function displayResults(data) {
            const resultsContent = document.getElementById('resultsContent');
            
            let html = `
                <div class="result-item">
                    <h3>📈 Summary</h3>
                    <p><strong>Total Estimated Count:</strong> ${data.total_estimated_count}</p>
                    <p><strong>Average Count per Frame:</strong> ${data.average_count_per_frame.toFixed(2)}</p>
                    <p><strong>Total Frames Analyzed:</strong> ${data.total_frames_analyzed}</p>
                </div>
            `;
            
            if (data.annotated_frames && data.annotated_frames.length > 0) {
                html += `
                    <div class="result-item">
                        <h3>🖼️ Sample Frames</h3>
                        <div class="frames-grid">
                `;
                
                data.annotated_frames.forEach((frameUrl, index) => {
                    html += `
                        <div class="frame-item">
                            <img src="${frameUrl}" alt="Frame ${index + 1}">
                            <p style="text-align: center; margin-top: 5px;">Frame ${index + 1}</p>
                        </div>
                    `;
                });
                
                html += `
                        </div>
                    </div>
                `;
            }
            
            if (data.details) {
                html += `
                    <div class="result-item">
                        <h3>🔧 Technical Details</h3>
                        <p><strong>Processing Time:</strong> ${data.details.processing_timestamp}</p>
                        <p><strong>YOLO Model:</strong> ${data.details.model_versions.yolo}</p>
                        <p><strong>Crowd Counting Model:</strong> ${data.details.model_versions.crowd_counting}</p>
                    </div>
                `;
            }
            
            resultsContent.innerHTML = html;
            document.getElementById('results').style.display = 'block';
        }
        
        // Clear URL when file is selected
        document.getElementById('videoFile').addEventListener('change', function() {
            if (this.files.length > 0) {
                document.getElementById('videoUrl').value = '';
            }
        });
        
        // Clear file when URL is entered
        document.getElementById('videoUrl').addEventListener('input', function() {
            if (this.value) {
                document.getElementById('videoFile').value = '';
            }
        });
    </script>
</body>
</html>
