import cv2
import numpy as np
from ultralytics import YOLO
import os
from typing import List, Dict, Tuple
import logging

logger = logging.getLogger(__name__)

class YOLOPersonDetector:
    """YOLO-based person detection for crowd counting"""
    
    def __init__(self, model_path: str = None):
        """
        Initialize YOLO detector
        
        Args:
            model_path: Path to YOLO model file. If None, uses pretrained YOLOv8n
        """
        self.model_path = model_path or "yolov8n.pt"  # Use nano version for speed
        self.model = None
        self.person_class_id = 0  # Person class ID in COCO dataset
        self.confidence_threshold = 0.5
        self.nms_threshold = 0.4
        
    def load_model(self):
        """Load the YOLO model"""
        try:
            logger.info(f"Loading YOLO model: {self.model_path}")
            self.model = YOLO(self.model_path)
            logger.info("YOLO model loaded successfully")
        except Exception as e:
            logger.error(f"Failed to load YOLO model: {e}")
            raise
    
    def detect_persons(self, image: np.ndarray) -> List[Dict]:
        """
        Detect persons in an image
        
        Args:
            image: Input image as numpy array
            
        Returns:
            List of detection dictionaries with bbox, confidence, etc.
        """
        if self.model is None:
            self.load_model()
        
        try:
            # Run inference
            results = self.model(image, conf=self.confidence_threshold, iou=self.nms_threshold)
            
            detections = []
            for result in results:
                boxes = result.boxes
                if boxes is not None:
                    for box in boxes:
                        # Check if detection is a person (class 0 in COCO)
                        class_id = int(box.cls[0])
                        if class_id == self.person_class_id:
                            # Get bounding box coordinates
                            x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                            confidence = float(box.conf[0])
                            
                            detections.append({
                                'bbox': [int(x1), int(y1), int(x2), int(y2)],
                                'confidence': confidence,
                                'class_id': class_id,
                                'class_name': 'person',
                                'center': [int((x1 + x2) / 2), int((y1 + y2) / 2)],
                                'area': int((x2 - x1) * (y2 - y1))
                            })
            
            logger.debug(f"Detected {len(detections)} persons")
            return detections
            
        except Exception as e:
            logger.error(f"Error during person detection: {e}")
            return []
    
    def annotate_image(self, image: np.ndarray, detections: List[Dict]) -> np.ndarray:
        """
        Draw bounding boxes and labels on the image
        
        Args:
            image: Input image
            detections: List of detection dictionaries
            
        Returns:
            Annotated image
        """
        annotated_image = image.copy()
        
        for detection in detections:
            bbox = detection['bbox']
            confidence = detection['confidence']
            
            # Draw bounding box
            cv2.rectangle(
                annotated_image,
                (bbox[0], bbox[1]),
                (bbox[2], bbox[3]),
                (0, 255, 0),  # Green color
                2
            )
            
            # Draw label
            label = f"Person: {confidence:.2f}"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 2)[0]
            
            # Draw label background
            cv2.rectangle(
                annotated_image,
                (bbox[0], bbox[1] - label_size[1] - 10),
                (bbox[0] + label_size[0], bbox[1]),
                (0, 255, 0),
                -1
            )
            
            # Draw label text
            cv2.putText(
                annotated_image,
                label,
                (bbox[0], bbox[1] - 5),
                cv2.FONT_HERSHEY_SIMPLEX,
                0.5,
                (0, 0, 0),  # Black text
                2
            )
        
        # Add count text
        count_text = f"Persons detected: {len(detections)}"
        cv2.putText(
            annotated_image,
            count_text,
            (10, 30),
            cv2.FONT_HERSHEY_SIMPLEX,
            1,
            (0, 0, 255),  # Red text
            2
        )
        
        return annotated_image
    
    def process_frame(self, frame: np.ndarray, save_path: str = None) -> Tuple[List[Dict], np.ndarray]:
        """
        Process a single frame: detect persons and create annotated image
        
        Args:
            frame: Input frame
            save_path: Optional path to save annotated frame
            
        Returns:
            Tuple of (detections, annotated_frame)
        """
        # Detect persons
        detections = self.detect_persons(frame)
        
        # Create annotated image
        annotated_frame = self.annotate_image(frame, detections)
        
        # Save annotated frame if path provided
        if save_path:
            cv2.imwrite(save_path, annotated_frame)
            logger.debug(f"Saved annotated frame to {save_path}")
        
        return detections, annotated_frame
    
    def get_model_info(self) -> Dict:
        """Get information about the loaded model"""
        if self.model is None:
            return {"status": "not_loaded"}
        
        return {
            "status": "loaded",
            "model_path": self.model_path,
            "model_type": "YOLOv8",
            "confidence_threshold": self.confidence_threshold,
            "nms_threshold": self.nms_threshold,
            "target_class": "person"
        }
