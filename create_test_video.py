#!/usr/bin/env python3
"""
Create a simple test video with moving rectangles to simulate people
"""

import cv2
import numpy as np
import os

def create_test_video(output_path="test_video.mp4", duration_seconds=10, fps=30):
    """
    Create a test video with moving rectangles that simulate people
    """
    width, height = 640, 480
    total_frames = duration_seconds * fps
    
    # Define video writer
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    
    # Define some "people" as moving rectangles
    people = [
        {"x": 50, "y": 200, "w": 30, "h": 80, "dx": 2, "dy": 1, "color": (0, 255, 0)},
        {"x": 150, "y": 150, "w": 25, "h": 70, "dx": -1, "dy": 2, "color": (255, 0, 0)},
        {"x": 300, "y": 300, "w": 35, "h": 90, "dx": 1, "dy": -1, "color": (0, 0, 255)},
        {"x": 400, "y": 100, "w": 28, "h": 75, "dx": -2, "dy": 1, "color": (255, 255, 0)},
        {"x": 500, "y": 250, "w": 32, "h": 85, "dx": 1, "dy": -2, "color": (255, 0, 255)},
    ]
    
    print(f"Creating test video: {output_path}")
    print(f"Duration: {duration_seconds}s, FPS: {fps}, Total frames: {total_frames}")
    
    for frame_num in range(total_frames):
        # Create blank frame
        frame = np.zeros((height, width, 3), dtype=np.uint8)
        
        # Add some background texture
        frame[:] = (20, 20, 20)  # Dark gray background
        
        # Draw and move people
        for person in people:
            # Draw rectangle (person)
            cv2.rectangle(
                frame,
                (person["x"], person["y"]),
                (person["x"] + person["w"], person["y"] + person["h"]),
                person["color"],
                -1
            )
            
            # Add a simple "head" circle
            head_x = person["x"] + person["w"] // 2
            head_y = person["y"] - 10
            cv2.circle(frame, (head_x, head_y), 8, person["color"], -1)
            
            # Move person
            person["x"] += person["dx"]
            person["y"] += person["dy"]
            
            # Bounce off walls
            if person["x"] <= 0 or person["x"] + person["w"] >= width:
                person["dx"] *= -1
            if person["y"] <= 20 or person["y"] + person["h"] >= height:
                person["dy"] *= -1
        
        # Add frame number text
        cv2.putText(
            frame,
            f"Frame: {frame_num + 1}/{total_frames}",
            (10, 30),
            cv2.FONT_HERSHEY_SIMPLEX,
            0.7,
            (255, 255, 255),
            2
        )
        
        # Add people count text
        cv2.putText(
            frame,
            f"People in scene: {len(people)}",
            (10, 60),
            cv2.FONT_HERSHEY_SIMPLEX,
            0.7,
            (255, 255, 255),
            2
        )
        
        # Write frame
        out.write(frame)
        
        if (frame_num + 1) % 30 == 0:
            print(f"Generated {frame_num + 1}/{total_frames} frames...")
    
    # Release video writer
    out.release()
    print(f"✅ Test video created: {output_path}")
    print(f"File size: {os.path.getsize(output_path) / 1024 / 1024:.2f} MB")
    
    return output_path

def main():
    """Create test video"""
    video_path = create_test_video("test_video.mp4", duration_seconds=5, fps=10)
    
    print(f"\nTest video created successfully!")
    print(f"You can now test the API with this video file.")
    print(f"Video path: {os.path.abspath(video_path)}")

if __name__ == "__main__":
    main()
