import cv2
import numpy as np
import os
from typing import List, Dict, Tuple
import logging

logger = logging.getLogger(__name__)

class VideoProcessor:
    """Utility class for video processing operations"""
    
    def __init__(self):
        self.supported_formats = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv']
    
    def validate_video_file(self, file_path: str) -> bool:
        """Validate if the file is a supported video format"""
        if not os.path.exists(file_path):
            return False
        
        _, ext = os.path.splitext(file_path.lower())
        return ext in self.supported_formats
    
    def get_video_info(self, video_path: str) -> Dict:
        """Get basic information about the video"""
        cap = cv2.VideoCapture(video_path)
        
        if not cap.isOpened():
            raise ValueError(f"Could not open video file: {video_path}")
        
        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        duration = frame_count / fps if fps > 0 else 0
        
        cap.release()
        
        return {
            'fps': fps,
            'frame_count': frame_count,
            'width': width,
            'height': height,
            'duration': duration,
            'resolution': f"{width}x{height}"
        }
    
    def extract_frames_at_intervals(
        self, 
        video_path: str, 
        interval_seconds: float = 1.0,
        output_dir: str = "static",
        max_frames: int = None
    ) -> List[Dict]:
        """
        Extract frames from video at specified intervals
        
        Args:
            video_path: Path to the video file
            interval_seconds: Interval between frames in seconds
            output_dir: Directory to save extracted frames
            max_frames: Maximum number of frames to extract
            
        Returns:
            List of frame information dictionaries
        """
        cap = cv2.VideoCapture(video_path)
        
        if not cap.isOpened():
            raise ValueError(f"Could not open video file: {video_path}")
        
        # Ensure output directory exists
        os.makedirs(output_dir, exist_ok=True)
        
        fps = cap.get(cv2.CAP_PROP_FPS)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        if fps <= 0:
            raise ValueError("Invalid video FPS")
        
        frame_interval = int(fps * interval_seconds)
        frames_data = []
        frame_count = 0
        extracted_count = 0
        
        logger.info(f"Extracting frames every {interval_seconds}s (every {frame_interval} frames)")
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            if frame_count % frame_interval == 0:
                if max_frames and extracted_count >= max_frames:
                    break
                
                # Generate frame filename
                timestamp = frame_count / fps
                frame_filename = f"frame_{extracted_count:04d}_{timestamp:.2f}s.jpg"
                frame_path = os.path.join(output_dir, frame_filename)
                
                # Save frame
                success = cv2.imwrite(frame_path, frame)
                if not success:
                    logger.warning(f"Failed to save frame {extracted_count}")
                    continue
                
                frames_data.append({
                    'frame_number': extracted_count,
                    'original_frame_number': frame_count,
                    'timestamp': timestamp,
                    'frame_path': frame_path,
                    'frame_filename': frame_filename,
                    'frame_url': f"/static/{frame_filename}",
                    'width': frame.shape[1],
                    'height': frame.shape[0]
                })
                
                extracted_count += 1
                
                if extracted_count % 10 == 0:
                    logger.info(f"Extracted {extracted_count} frames...")
            
            frame_count += 1
        
        cap.release()
        logger.info(f"Successfully extracted {extracted_count} frames from {total_frames} total frames")
        
        return frames_data
    
    def resize_frame(self, frame: np.ndarray, max_width: int = 1920, max_height: int = 1080) -> np.ndarray:
        """Resize frame while maintaining aspect ratio"""
        height, width = frame.shape[:2]
        
        if width <= max_width and height <= max_height:
            return frame
        
        # Calculate scaling factor
        scale_w = max_width / width
        scale_h = max_height / height
        scale = min(scale_w, scale_h)
        
        new_width = int(width * scale)
        new_height = int(height * scale)
        
        return cv2.resize(frame, (new_width, new_height), interpolation=cv2.INTER_AREA)
    
    def cleanup_frames(self, frames_data: List[Dict]) -> None:
        """Clean up extracted frame files"""
        for frame_info in frames_data:
            frame_path = frame_info.get('frame_path')
            if frame_path and os.path.exists(frame_path):
                try:
                    os.remove(frame_path)
                    logger.debug(f"Removed frame file: {frame_path}")
                except Exception as e:
                    logger.warning(f"Failed to remove frame file {frame_path}: {e}")
