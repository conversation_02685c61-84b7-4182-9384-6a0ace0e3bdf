#!/usr/bin/env python3
"""
Test script for video analysis API
"""

import requests
import json
import time

def test_video_url():
    """Test video analysis with a URL"""
    
    # Use a sample video URL (you can replace this with any public video URL)
    test_url = "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4"
    
    print("Testing video analysis with URL...")
    print(f"Video URL: {test_url}")
    
    # Prepare the request
    data = {
        "video_url": test_url
    }
    
    try:
        # Send request
        print("Sending request to API...")
        response = requests.post("http://localhost:8000/analyze-video", data=data)
        
        if response.status_code == 200:
            results = response.json()
            print("\n✅ Analysis successful!")
            print(f"Total estimated count: {results['total_estimated_count']}")
            print(f"Average count per frame: {results['average_count_per_frame']:.2f}")
            print(f"Total frames analyzed: {results['total_frames_analyzed']}")
            print(f"Annotated frames: {len(results['annotated_frames'])}")
            
            # Print model information
            if 'details' in results and 'model_versions' in results['details']:
                print("\nModel Information:")
                print(f"YOLO: {results['details']['model_versions']['yolo']}")
                print(f"Crowd Counting: {results['details']['model_versions']['crowd_counting']}")
            
            # Print summary statistics if available
            if 'details' in results and 'summary_statistics' in results['details']:
                stats = results['details']['summary_statistics']
                print("\nSummary Statistics:")
                print(f"Max count per frame: {stats.get('max_count_per_frame', 'N/A')}")
                print(f"Min count per frame: {stats.get('min_count_per_frame', 'N/A')}")
                print(f"Average density score: {stats.get('avg_density_score', 'N/A'):.3f}")
            
            return True
            
        else:
            print(f"❌ Request failed with status code: {response.status_code}")
            print(f"Error: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_health_endpoint():
    """Test the health endpoint"""
    try:
        response = requests.get("http://localhost:8000/health")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Health check passed: {data['status']}")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False

def main():
    print("🎥 AI Crowd POC - Testing Video Analysis")
    print("=" * 50)
    
    # Test health endpoint first
    print("1. Testing health endpoint...")
    if not test_health_endpoint():
        print("Server is not responding. Please make sure it's running.")
        return
    
    print("\n2. Testing video analysis...")
    success = test_video_url()
    
    if success:
        print("\n🎉 All tests passed!")
        print("You can now open http://localhost:8000 in your browser to use the web interface.")
    else:
        print("\n❌ Some tests failed. Check the server logs for more details.")

if __name__ == "__main__":
    main()
