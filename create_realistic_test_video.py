#!/usr/bin/env python3
"""
Create a more realistic test video with human-like shapes
"""

import cv2
import numpy as np
import os

def draw_person(frame, x, y, scale=1.0, color=(255, 255, 255)):
    """Draw a simple human-like figure"""
    # Scale factors
    head_radius = int(8 * scale)
    body_height = int(40 * scale)
    body_width = int(15 * scale)
    leg_height = int(25 * scale)
    arm_length = int(20 * scale)
    
    # Head
    cv2.circle(frame, (x, y), head_radius, color, -1)
    
    # Body (rectangle)
    body_top = y + head_radius + 2
    body_bottom = body_top + body_height
    cv2.rectangle(frame, 
                  (x - body_width//2, body_top), 
                  (x + body_width//2, body_bottom), 
                  color, -1)
    
    # Arms
    arm_y = body_top + body_height//3
    # Left arm
    cv2.line(frame, (x - body_width//2, arm_y), 
             (x - body_width//2 - arm_length, arm_y + arm_length//2), color, 3)
    # Right arm
    cv2.line(frame, (x + body_width//2, arm_y), 
             (x + body_width//2 + arm_length, arm_y + arm_length//2), color, 3)
    
    # Legs
    leg_start_y = body_bottom
    leg_end_y = leg_start_y + leg_height
    # Left leg
    cv2.line(frame, (x - body_width//4, leg_start_y), 
             (x - body_width//2, leg_end_y), color, 4)
    # Right leg
    cv2.line(frame, (x + body_width//4, leg_start_y), 
             (x + body_width//2, leg_end_y), color, 4)

def create_realistic_test_video(output_path="realistic_test_video.mp4", duration_seconds=8, fps=15):
    """
    Create a test video with more realistic human-like figures
    """
    width, height = 800, 600
    total_frames = duration_seconds * fps
    
    # Define video writer
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    
    # Define some "people" with more realistic movement
    people = [
        {
            "x": 100, "y": 150, "dx": 1.5, "dy": 0.3, 
            "scale": 1.2, "color": (200, 200, 255),
            "walk_cycle": 0
        },
        {
            "x": 200, "y": 200, "dx": -0.8, "dy": 0.5, 
            "scale": 0.9, "color": (255, 200, 200),
            "walk_cycle": 0
        },
        {
            "x": 350, "y": 180, "dx": 1.2, "dy": -0.4, 
            "scale": 1.1, "color": (200, 255, 200),
            "walk_cycle": 0
        },
        {
            "x": 500, "y": 250, "dx": -1.0, "dy": 0.8, 
            "scale": 1.0, "color": (255, 255, 200),
            "walk_cycle": 0
        },
        {
            "x": 650, "y": 300, "dx": 0.7, "dy": -0.6, 
            "scale": 0.8, "color": (255, 200, 255),
            "walk_cycle": 0
        },
        {
            "x": 300, "y": 400, "dx": 1.3, "dy": 0.2, 
            "scale": 1.3, "color": (200, 255, 255),
            "walk_cycle": 0
        },
        {
            "x": 450, "y": 350, "dx": -0.9, "dy": -0.7, 
            "scale": 0.95, "color": (255, 180, 180),
            "walk_cycle": 0
        }
    ]
    
    print(f"Creating realistic test video: {output_path}")
    print(f"Duration: {duration_seconds}s, FPS: {fps}, Total frames: {total_frames}")
    print(f"People in scene: {len(people)}")
    
    for frame_num in range(total_frames):
        # Create frame with gradient background
        frame = np.zeros((height, width, 3), dtype=np.uint8)
        
        # Add gradient background
        for i in range(height):
            intensity = int(30 + (i / height) * 50)
            frame[i, :] = (intensity, intensity, intensity + 10)
        
        # Add some "ground" texture
        ground_y = int(height * 0.85)
        cv2.rectangle(frame, (0, ground_y), (width, height), (40, 60, 40), -1)
        
        # Draw and move people
        for person in people:
            # Add some walking animation
            person["walk_cycle"] += 0.3
            walk_offset_y = int(2 * np.sin(person["walk_cycle"]))
            
            # Draw person with walking animation
            draw_person(
                frame, 
                int(person["x"]), 
                int(person["y"]) + walk_offset_y, 
                person["scale"], 
                person["color"]
            )
            
            # Move person
            person["x"] += person["dx"]
            person["y"] += person["dy"]
            
            # Bounce off walls with some randomness
            if person["x"] <= 50 or person["x"] >= width - 50:
                person["dx"] *= -1
                person["dx"] += np.random.uniform(-0.2, 0.2)  # Add some randomness
            if person["y"] <= 100 or person["y"] >= height - 150:
                person["dy"] *= -1
                person["dy"] += np.random.uniform(-0.2, 0.2)  # Add some randomness
            
            # Keep people within bounds
            person["x"] = max(50, min(width - 50, person["x"]))
            person["y"] = max(100, min(height - 150, person["y"]))
        
        # Add frame information
        cv2.putText(
            frame,
            f"Frame: {frame_num + 1}/{total_frames}",
            (10, 30),
            cv2.FONT_HERSHEY_SIMPLEX,
            0.7,
            (255, 255, 255),
            2
        )
        
        # Add people count
        cv2.putText(
            frame,
            f"People in scene: {len(people)}",
            (10, 60),
            cv2.FONT_HERSHEY_SIMPLEX,
            0.7,
            (255, 255, 255),
            2
        )
        
        # Add timestamp
        timestamp = frame_num / fps
        cv2.putText(
            frame,
            f"Time: {timestamp:.1f}s",
            (10, 90),
            cv2.FONT_HERSHEY_SIMPLEX,
            0.7,
            (255, 255, 255),
            2
        )
        
        # Write frame
        out.write(frame)
        
        if (frame_num + 1) % 30 == 0:
            print(f"Generated {frame_num + 1}/{total_frames} frames...")
    
    # Release video writer
    out.release()
    print(f"✅ Realistic test video created: {output_path}")
    print(f"File size: {os.path.getsize(output_path) / 1024 / 1024:.2f} MB")
    
    return output_path

def main():
    """Create realistic test video"""
    video_path = create_realistic_test_video("realistic_test_video.mp4", duration_seconds=6, fps=12)
    
    print(f"\nRealistic test video created successfully!")
    print(f"This video contains {7} human-like figures that should be detectable by YOLO.")
    print(f"Video path: {os.path.abspath(video_path)}")
    print(f"\nTo test with this video:")
    print(f"python test_video_upload.py")

if __name__ == "__main__":
    main()
